{"mcpServers": {"github": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "-e", "GITHUB_TOOLSETS", "-e", "GITHUB_READ_ONLY", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************", "GITHUB_TOOLSETS": "", "GITHUB_READ_ONLY": ""}}, "github.com/Operative-Sh/web-eval-agent": {"command": "uvx", "args": ["--refresh-package", "webEvalAgent", "--from", "git+https://github.com/Operative-Sh/web-eval-agent.git", "webEvalAgent"], "env": {"OPERATIVE_API_KEY": "${OPERATIVE_API_KEY}"}}}}